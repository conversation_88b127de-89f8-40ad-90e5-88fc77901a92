package me.myot233.booksystem.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 首页控制器
 */
@Controller
public class HomeController {

    /**
     * 根路径映射
     * @return 欢迎页面
     */
    @GetMapping("/")
    @ResponseBody
    public String home() {
        return "<!DOCTYPE html>\n" +
               "<html lang='zh-CN'>\n" +
               "<head>\n" +
               "    <meta charset='UTF-8'>\n" +
               "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n" +
               "    <title>图书管理系统</title>\n" +
               "    <style>\n" +
               "        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }\n" +
               "        .container { text-align: center; }\n" +
               "        .api-list { text-align: left; margin: 20px 0; }\n" +
               "        .api-item { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 5px; }\n" +
               "        .btn { display: inline-block; padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }\n" +
               "        .btn:hover { background: #0056b3; }\n" +
               "    </style>\n" +
               "</head>\n" +
               "<body>\n" +
               "    <div class='container'>\n" +
               "        <h1>📚 图书管理系统</h1>\n" +
               "        <p>欢迎使用图书管理系统！系统已成功启动。</p>\n" +
               "        \n" +
               "        <div class='api-list'>\n" +
               "            <h3>🚀 可用的API端点：</h3>\n" +
               "            <div class='api-item'><strong>GET</strong> <a href='/api/books/' target='_blank'>/api/books/</a> - 获取所有图书</div>\n" +
               "            <div class='api-item'><strong>POST</strong> /api/auth/login - 用户登录</div>\n" +
               "            <div class='api-item'><strong>POST</strong> /api/auth/register - 用户注册</div>\n" +
               "            <div class='api-item'><strong>GET</strong> /api/users/me - 获取当前用户信息</div>\n" +
               "            <div class='api-item'><strong>GET</strong> /api/notifications - 获取通知列表</div>\n" +
               "        </div>\n" +
               "        \n" +
               "        <div>\n" +
               "            <h3>🔧 测试工具：</h3>\n" +
               "            <a href='/test' class='btn'>🧪 WebSocket测试页面</a>\n" +
               "        </div>\n" +
               "        \n" +
               "        <div style='margin-top: 30px;'>\n" +
               "            <h3>🔑 测试账户：</h3>\n" +
               "            <p><strong>管理员：</strong> admin / admin123</p>\n" +
               "            <p><strong>普通用户：</strong> user / user123</p>\n" +
               "        </div>\n" +
               "        \n" +
               "        <div style='margin-top: 30px; color: #28a745;'>\n" +
               "            <p>✅ 服务器运行正常！</p>\n" +
               "            <p>✅ JWT认证系统已启用</p>\n" +
               "            <p>✅ WebSocket实时通知已启用</p>\n" +
               "        </div>\n" +
               "    </div>\n" +
               "</body>\n" +
               "</html>";
    }

    /**
     * WebSocket测试页面
     * @return 测试页面
     */
    @GetMapping("/test")
    @ResponseBody
    public String testPage() {
        try {
            // 读取静态文件内容
            return java.nio.file.Files.readString(
                java.nio.file.Paths.get("src/main/resources/static/index.html")
            );
        } catch (Exception e) {
            return "<h1>测试页面加载失败</h1><p>请检查 src/main/resources/static/index.html 文件是否存在</p><p>错误：" + e.getMessage() + "</p>";
        }
    }
}
