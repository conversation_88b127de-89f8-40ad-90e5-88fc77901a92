<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图书管理系统 - WebSocket测试</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 3px;
            background-color: white;
        }
        .notification {
            border-left: 4px solid #007bff;
        }
        .system {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <h1>图书管理系统 - WebSocket实时通知测试</h1>
    
    <div class="container">
        <!-- 连接控制面板 -->
        <div class="panel">
            <h3>连接控制</h3>
            <div id="connectionStatus" class="status disconnected">未连接</div>
            
            <div class="form-group">
                <label for="jwtToken">JWT Token (可选):</label>
                <input type="text" id="jwtToken" placeholder="Bearer token...">
            </div>
            
            <button onclick="connect()">连接WebSocket</button>
            <button onclick="disconnect()">断开连接</button>
            
            <h4>API测试</h4>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="admin123">
            </div>
            <button onclick="login()">登录获取Token</button>
        </div>
        
        <!-- 消息发送面板 -->
        <div class="panel">
            <h3>发送测试消息</h3>
            
            <div class="form-group">
                <label for="broadcastTitle">广播标题:</label>
                <input type="text" id="broadcastTitle" value="系统维护通知">
            </div>
            <div class="form-group">
                <label for="broadcastContent">广播内容:</label>
                <textarea id="broadcastContent" rows="3">系统将于今晚22:00-24:00进行维护，请提前保存工作。</textarea>
            </div>
            <button onclick="sendBroadcast()">发送系统广播</button>
            
            <hr>
            
            <div class="form-group">
                <label for="bookTitle">新书标题:</label>
                <input type="text" id="bookTitle" value="人工智能导论">
            </div>
            <button onclick="addNewBook()">添加新书(触发通知)</button>
        </div>
    </div>
    
    <!-- 消息显示区域 -->
    <div class="panel" style="margin-top: 20px;">
        <h3>实时消息</h3>
        <div id="messages" class="messages"></div>
        <button onclick="clearMessages()">清空消息</button>
    </div>

    <script>
        let stompClient = null;
        let currentToken = null;

        function connect() {
            const socket = new SockJS('/ws');
            stompClient = Stomp.over(socket);
            
            const token = document.getElementById('jwtToken').value;
            const headers = {};
            if (token) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : 'Bearer ' + token;
            }
            
            stompClient.connect(headers, function (frame) {
                updateConnectionStatus(true);
                addMessage('系统', '已连接到WebSocket服务器', 'system');
                
                // 订阅系统通知
                stompClient.subscribe('/topic/system-notifications', function (message) {
                    const notification = JSON.parse(message.body);
                    addMessage('系统广播', notification.content, 'notification');
                });
                
                // 订阅新书通知
                stompClient.subscribe('/topic/new-books', function (message) {
                    const notification = JSON.parse(message.body);
                    addMessage('新书通知', notification.content, 'notification');
                });
                
                // 如果有token，订阅个人通知
                if (token) {
                    // 这里需要从token中解析用户ID，简化处理
                    stompClient.subscribe('/user/queue/notifications', function (message) {
                        const notification = JSON.parse(message.body);
                        addMessage('个人通知', notification.content, 'notification');
                    });
                    
                    stompClient.subscribe('/user/queue/unread-count', function (message) {
                        const count = message.body;
                        addMessage('未读数量', '未读通知: ' + count, 'system');
                    });
                }
                
            }, function (error) {
                updateConnectionStatus(false);
                addMessage('错误', '连接失败: ' + error, 'error');
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
            updateConnectionStatus(false);
            addMessage('系统', '已断开WebSocket连接', 'system');
        }

        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            if (connected) {
                statusElement.textContent = '已连接';
                statusElement.className = 'status connected';
            } else {
                statusElement.textContent = '未连接';
                statusElement.className = 'status disconnected';
            }
        }

        function addMessage(type, content, className) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + className;
            messageDiv.innerHTML = `
                <strong>${type}</strong> [${new Date().toLocaleTimeString()}]<br>
                ${content}
            `;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.token;
                    document.getElementById('jwtToken').value = 'Bearer ' + data.token;
                    addMessage('登录', `登录成功，用户: ${data.username}, 角色: ${data.role}`, 'system');
                } else {
                    addMessage('登录', '登录失败', 'error');
                }
            } catch (error) {
                addMessage('登录', '登录错误: ' + error.message, 'error');
            }
        }

        async function sendBroadcast() {
            const title = document.getElementById('broadcastTitle').value;
            const content = document.getElementById('broadcastContent').value;
            
            if (!currentToken) {
                addMessage('错误', '请先登录获取token', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/notifications/broadcast', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + currentToken
                    },
                    body: JSON.stringify({ title, content })
                });
                
                if (response.ok) {
                    addMessage('广播', '系统广播发送成功', 'system');
                } else {
                    addMessage('广播', '发送失败，可能需要管理员权限', 'error');
                }
            } catch (error) {
                addMessage('广播', '发送错误: ' + error.message, 'error');
            }
        }

        async function addNewBook() {
            const title = document.getElementById('bookTitle').value;
            
            try {
                const response = await fetch('/api/books', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        author: '测试作者',
                        category: '测试分类',
                        publisher: '测试出版社',
                        isbn: '978-' + Math.random().toString().substr(2, 10),
                        stock: 5,
                        borrowed: 0
                    })
                });
                
                if (response.ok) {
                    addMessage('新书', '新书添加成功，应该会收到通知', 'system');
                } else {
                    addMessage('新书', '添加失败', 'error');
                }
            } catch (error) {
                addMessage('新书', '添加错误: ' + error.message, 'error');
            }
        }

        // 页面加载时的提示
        window.onload = function() {
            addMessage('系统', '页面已加载，请先连接WebSocket服务器', 'system');
        };
    </script>
</body>
</html>
