# 图书管理系统 - WebSocket实时通知与JWT认证实现报告

## 📋 项目概述

本报告详细记录了为图书管理系统添加WebSocket实时通知功能和JWT认证系统的完整实现过程。

### 🎯 项目目标
- 实现JWT用户认证系统
- 开发WebSocket实时通知功能
- 添加新书到达、借阅提醒等通知
- 构建系统消息推送模块

### 🏗️ 技术栈
- **后端框架**: Spring Boot 3.4.5
- **安全框架**: Spring Security
- **WebSocket**: Spring WebSocket + STOMP
- **JWT**: JJWT 0.12.3
- **数据库**: MySQL + JPA
- **前端**: HTML + JavaScript + SockJS + STOMP.js

---

## 🔧 实现的功能模块

### 1. JWT认证系统

#### 📦 添加的依赖
```xml
<!-- JWT dependencies -->
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-api</artifactId>
    <version>0.12.3</version>
</dependency>
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-impl</artifactId>
    <version>0.12.3</version>
    <scope>runtime</scope>
</dependency>
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-jackson</artifactId>
    <version>0.12.3</version>
    <scope>runtime</scope>
</dependency>
```

#### 📁 创建的核心文件
- `src/main/java/me/myot233/booksystem/util/JwtUtil.java` - JWT工具类
- `src/main/java/me/myot233/booksystem/security/JwtAuthenticationFilter.java` - JWT认证过滤器
- 更新 `SecurityConfig.java` - 集成JWT到Spring Security
- 更新 `AuthController.java` - 登录返回JWT token

#### 🔐 实现原理
```java
// 1. 用户登录时生成JWT token
String token = jwtUtil.generateTokenWithRole(userDetails, user.getRole());
String refreshToken = jwtUtil.generateRefreshToken(userDetails);

// 2. 每次请求通过过滤器验证token
if (jwtUtil.validateToken(jwtToken, userDetails)) {
    UsernamePasswordAuthenticationToken authToken =
        new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    SecurityContextHolder.getContext().setAuthentication(authToken);
}
```

#### ⚙️ JWT配置
```properties
# JWT Configuration
jwt.expiration=86400000          # 24小时
jwt.refresh-expiration=604800000 # 7天
```

### 2. WebSocket实时通知系统

#### 📁 创建的核心文件
- `src/main/java/me/myot233/booksystem/config/WebSocketConfig.java` - WebSocket配置
- `src/main/java/me/myot233/booksystem/controller/WebSocketController.java` - WebSocket消息控制器
- `src/main/java/me/myot233/booksystem/entity/Notification.java` - 通知实体类
- `src/main/java/me/myot233/booksystem/repository/NotificationRepository.java` - 通知数据访问层
- `src/main/java/me/myot233/booksystem/service/NotificationService.java` - 通知业务逻辑
- `src/main/java/me/myot233/booksystem/controller/NotificationController.java` - 通知REST API

#### 🏗️ 系统架构
```
客户端 ←→ WebSocket ←→ STOMP协议 ←→ Spring消息代理 ←→ 业务服务
```

#### 📡 消息频道设计
- `/topic/system-notifications` - 系统广播（所有用户）
- `/topic/new-books` - 新书通知（所有用户）
- `/user/queue/notifications` - 个人通知（特定用户）
- `/user/queue/unread-count` - 未读数量（特定用户）

#### 🔧 WebSocket配置
```java
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        config.enableSimpleBroker("/topic", "/queue");
        config.setApplicationDestinationPrefixes("/app");
        config.setUserDestinationPrefix("/user");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*")
                .withSockJS();
    }
}
```

### 3. 通知功能模块

#### 📊 数据库设计
```sql
-- 通知表
CREATE TABLE notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    book_id BIGINT
);
```

#### 🏷️ 通知类型枚举
```java
public enum NotificationType {
    NEW_BOOK("新书到达"),
    BORROW_REMINDER("借阅提醒"),
    RETURN_REMINDER("归还提醒"),
    OVERDUE_REMINDER("逾期提醒"),
    SYSTEM_MESSAGE("系统消息"),
    BOOK_AVAILABLE("图书可借");
}
```

#### 🚀 核心功能实现

**1. 新书到达通知**
```java
// 在BookController中，添加新书时触发
@PostMapping
public ResponseEntity<Book> addBook(@RequestBody Book book) {
    Book savedBook = bookService.saveBook(book);
    // 发送新书到达通知
    notificationService.sendNewBookNotification(savedBook.getTitle(), savedBook.getId());
    return new ResponseEntity<>(savedBook, HttpStatus.CREATED);
}
```

**2. 实时消息推送**
```java
// 通过WebSocket发送实时通知
public void sendRealTimeNotification(Long userId, Notification notification) {
    messagingTemplate.convertAndSendToUser(
        userId.toString(),
        "/queue/notifications",
        notification
    );

    // 同时发送未读通知数量
    long unreadCount = getUnreadNotificationCount(userId);
    messagingTemplate.convertAndSendToUser(
        userId.toString(),
        "/queue/unread-count",
        unreadCount
    );
}
```

**3. 系统广播**
```java
// 管理员发送系统广播
public void broadcastSystemNotification(String title, String content) {
    Notification notification = new Notification();
    notification.setTitle(title);
    notification.setContent(content);
    notification.setType(NotificationType.SYSTEM_MESSAGE);

    messagingTemplate.convertAndSend("/topic/system-notifications", notification);
}
```

### 4. 定时任务系统

#### 📁 创建的文件
- `src/main/java/me/myot233/booksystem/service/ScheduledNotificationService.java` - 定时通知服务
- 更新 `BookSystemApplication.java` - 启用定时任务

#### ⏰ 定时任务功能
```java
// 每天上午9点检查借阅到期
@Scheduled(cron = "0 0 9 * * ?")
public void checkBorrowingDueDates() {
    List<User> users = userRepository.findAll();
    for (User user : users) {
        List<Book> borrowedBooks = user.getBorrowedBooks();
        for (Book book : borrowedBooks) {
            // 检查借阅到期情况，发送提醒
            notificationService.sendReturnReminder(
                user.getId(), book.getTitle(), book.getId(), 3);
        }
    }
}

// 每小时检查逾期情况
@Scheduled(cron = "0 0 * * * ?")
public void checkOverdueBooks() {
    // 检查逾期图书，发送逾期提醒
}

// 每周日晚上8点发送系统维护通知
@Scheduled(cron = "0 0 20 * * SUN")
public void sendWeeklyMaintenance() {
    notificationService.broadcastSystemNotification(
        "系统维护通知",
        "系统将于本周日晚上22:00-24:00进行例行维护。"
    );
}
```

### 5. 系统信息接口

#### 📁 创建的文件
- `src/main/java/me/myot233/booksystem/controller/HomeController.java` - 系统信息控制器

#### 🎨 系统接口功能
- 系统状态信息
- API端点列表
- 健康检查接口
- 测试账户信息

#### 💻 WebSocket客户端连接示例
```javascript
// 连接WebSocket
const socket = new SockJS('/ws');
const stompClient = Stomp.over(socket);

// 使用JWT token连接
const headers = {
    'Authorization': 'Bearer ' + jwtToken
};

stompClient.connect(headers, function(frame) {
    // 订阅个人通知
    stompClient.subscribe('/user/queue/notifications', function(message) {
        const notification = JSON.parse(message.body);
        handleNotification(notification);
    });

    // 订阅系统广播
    stompClient.subscribe('/topic/system-notifications', function(message) {
        const notification = JSON.parse(message.body);
        handleSystemMessage(notification);
    });
});
```

---

## 🔄 系统流程

### 1. JWT认证流程
```
1. 用户登录 → 验证用户名密码
2. 生成JWT token → 包含用户信息和角色
3. 返回token给客户端
4. 客户端请求携带token → Authorization: Bearer <token>
5. JWT过滤器验证token → 设置Spring Security上下文
6. 业务逻辑执行
```

### 2. WebSocket通信流程
```
1. 客户端连接 → SockJS + STOMP协议
2. 服务端配置消息代理 → /topic, /queue, /user前缀
3. 客户端订阅频道 → 接收特定类型消息
4. 服务端发送消息 → 通过SimpMessagingTemplate
5. 客户端接收消息 → 实时显示通知
```

### 3. 通知推送流程
```
1. 业务事件触发 → 新书添加、借阅到期等
2. 创建通知对象 → 保存到数据库
3. 实时推送 → 通过WebSocket发送给用户
4. 更新未读数量 → 实时更新用户界面
5. 用户标记已读 → 更新数据库状态
```

---

## 🚀 新增的API端点

### 系统信息相关
- `GET /` - 获取系统信息和API列表
- `GET /health` - 系统健康检查

### 认证相关
- `POST /api/auth/login` - 用户登录（返回JWT token）
- `POST /api/auth/register` - 用户注册

### 通知相关
- `GET /api/notifications` - 获取用户所有通知
- `GET /api/notifications/unread` - 获取未读通知
- `GET /api/notifications/unread/count` - 获取未读通知数量
- `PUT /api/notifications/{id}/read` - 标记指定通知为已读
- `PUT /api/notifications/read-all` - 标记所有通知为已读
- `POST /api/notifications/broadcast` - 发送系统广播（仅管理员）

### WebSocket端点
- `/ws` - WebSocket连接端点

---

## 🔒 安全配置更新

```java
@Bean
public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    http
        .csrf(AbstractHttpConfigurer::disable)
        .authorizeHttpRequests(authorize -> authorize
            .requestMatchers("/api/auth/**").permitAll()     // 允许认证相关请求
            .requestMatchers("/ws/**").permitAll()           // 允许WebSocket连接
            .requestMatchers("/api/books/**").permitAll()    // 允许图书相关请求
            .requestMatchers("/api/users/me").authenticated() // 当前用户信息需要认证
            .requestMatchers("/api/users/**").hasRole("ADMIN") // 用户管理需要管理员权限
            .anyRequest().authenticated()                     // 其他请求需要认证
        )
        .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

    return http.build();
}
```

---

## 🎨 功能特性

### 1. 实时性
- ✅ WebSocket长连接，消息实时推送
- ✅ 无需客户端轮询，减少服务器压力
- ✅ 支持断线重连机制

### 2. 安全性
- ✅ JWT token认证，无状态设计
- ✅ 用户权限控制，管理员才能发送系统广播
- ✅ Token过期自动处理

### 3. 可扩展性
- ✅ 通知类型枚举，易于添加新类型
- ✅ 消息频道分离，支持不同业务场景
- ✅ 模块化设计，便于功能扩展

### 4. 用户体验
- ✅ 未读消息计数实时更新
- ✅ 消息已读状态管理
- ✅ 个性化通知推送
- ✅ 消息历史记录查询

### 5. 系统监控
- ✅ 定时任务自动检查
- ✅ 系统维护通知
- ✅ 借阅到期提醒
- ✅ 逾期图书监控

---

## 🧪 测试指南

### 1. 启动应用
```bash
# 使用Maven启动
mvn spring-boot:run

# 或者使用IDE直接运行BookSystemApplication.java
```

### 2. 访问测试页面
```
浏览器访问: http://localhost:8080
```

### 3. 测试账户
- **管理员账户**: `admin` / `admin123`
- **普通用户账户**: `user` / `user123`

### 4. 测试流程
1. **登录测试**: 使用测试账户登录获取JWT token
2. **WebSocket连接**: 点击"连接WebSocket"按钮
3. **订阅通知**: 自动订阅相关通知频道
4. **新书通知**: 添加新书触发实时通知
5. **系统广播**: 使用管理员账户发送系统广播
6. **消息接收**: 查看实时消息接收情况

### 5. API测试
```bash
# 登录获取token
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 获取通知列表
curl -X GET http://localhost:8080/api/notifications \
  -H "Authorization: Bearer <your-jwt-token>"

# 发送系统广播
curl -X POST http://localhost:8080/api/notifications/broadcast \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{"title":"测试通知","content":"这是一条测试消息"}'
```

---

## 📚 API调用文档

### 1. 系统信息接口

#### 获取系统信息
```bash
GET /
Content-Type: application/json

# 响应示例
{
  "service": "图书管理系统 API",
  "version": "v1.0.0",
  "status": "running",
  "features": ["JWT认证", "WebSocket实时通知", "图书管理", "用户管理"],
  "endpoints": {
    "GET /api/books": "获取图书列表",
    "POST /api/auth/login": "用户登录",
    "WebSocket /ws": "实时通知连接"
  },
  "testAccounts": {
    "admin": "admin123 (管理员)",
    "user": "user123 (普通用户)"
  }
}
```

#### 健康检查
```bash
GET /health
Content-Type: application/json

# 响应示例
{
  "status": "UP",
  "timestamp": *************,
  "services": {
    "database": "UP",
    "jwt": "UP",
    "websocket": "UP"
  }
}
```

### 2. 认证相关API

#### 用户登录
```bash
POST /api/auth/login
Content-Type: application/json

# 请求体
{
  "username": "admin",
  "password": "admin123"
}

# 响应示例
{
  "message": "登录成功",
  "username": "admin",
  "token": "eyJhbGciOiJIUzUxMiJ9...",
  "refreshToken": "eyJhbGciOiJIUzUxMiJ9...",
  "role": "ROLE_ADMIN"
}
```

#### 用户注册
```bash
POST /api/auth/register
Content-Type: application/json

# 请求体
{
  "username": "newuser",
  "password": "password123",
  "realName": "新用户",
  "email": "<EMAIL>"
}

# 响应示例
{
  "message": "注册成功",
  "username": "newuser"
}
```

### 3. 通知相关API

#### 获取用户所有通知
```bash
GET /api/notifications
Authorization: Bearer <jwt-token>

# 响应示例
[
  {
    "id": 1,
    "title": "新书到达",
    "content": "新书《人工智能导论》已到达图书馆",
    "type": "NEW_BOOK",
    "isRead": false,
    "createTime": "2025-01-25T10:30:00",
    "bookId": 123
  }
]
```

#### 获取未读通知
```bash
GET /api/notifications/unread
Authorization: Bearer <jwt-token>
```

#### 获取未读通知数量
```bash
GET /api/notifications/unread/count
Authorization: Bearer <jwt-token>

# 响应示例
{
  "count": 5
}
```

#### 标记通知为已读
```bash
PUT /api/notifications/{notificationId}/read
Authorization: Bearer <jwt-token>

# 响应示例
{
  "message": "标记成功"
}
```

#### 标记所有通知为已读
```bash
PUT /api/notifications/read-all
Authorization: Bearer <jwt-token>

# 响应示例
{
  "message": "所有通知已标记为已读"
}
```

#### 发送系统广播（仅管理员）
```bash
POST /api/notifications/broadcast
Content-Type: application/json
Authorization: Bearer <admin-jwt-token>

# 请求体
{
  "title": "系统维护通知",
  "content": "系统将于今晚22:00-24:00进行维护。"
}

# 响应示例
{
  "message": "系统通知发送成功"
}
```

### 4. WebSocket连接

#### JavaScript客户端示例
```javascript
// 引入依赖
// <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
// <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>

// 连接WebSocket
const socket = new SockJS('/ws');
const stompClient = Stomp.over(socket);

// 使用JWT token连接
const headers = {
    'Authorization': 'Bearer ' + jwtToken
};

stompClient.connect(headers, function(frame) {
    console.log('已连接到WebSocket服务器');

    // 订阅个人通知
    stompClient.subscribe('/user/queue/notifications', function(message) {
        const notification = JSON.parse(message.body);
        console.log('收到个人通知:', notification);
        handleNotification(notification);
    });

    // 订阅未读数量更新
    stompClient.subscribe('/user/queue/unread-count', function(message) {
        const count = parseInt(message.body);
        console.log('未读通知数量:', count);
        updateUnreadCount(count);
    });

    // 订阅系统广播
    stompClient.subscribe('/topic/system-notifications', function(message) {
        const notification = JSON.parse(message.body);
        console.log('收到系统广播:', notification);
        handleSystemMessage(notification);
    });

    // 订阅新书通知
    stompClient.subscribe('/topic/new-books', function(message) {
        const notification = JSON.parse(message.body);
        console.log('收到新书通知:', notification);
        handleNewBookNotification(notification);
    });

}, function(error) {
    console.error('WebSocket连接失败:', error);
});

// 发送心跳消息（可选）
function sendHeartbeat() {
    if (stompClient && stompClient.connected) {
        stompClient.send('/app/heartbeat', {}, 'ping');
    }
}

// 每30秒发送一次心跳
setInterval(sendHeartbeat, 30000);
```

### 5. 错误处理

#### 常见错误响应
```json
# 401 未授权
{
  "timestamp": "2025-01-25T10:30:00.000+00:00",
  "status": 401,
  "error": "Unauthorized",
  "message": "JWT token无效或已过期",
  "path": "/api/notifications"
}

# 403 禁止访问
{
  "timestamp": "2025-01-25T10:30:00.000+00:00",
  "status": 403,
  "error": "Forbidden",
  "message": "权限不足",
  "path": "/api/notifications/broadcast"
}

# 404 资源不存在
{
  "timestamp": "2025-01-25T10:30:00.000+00:00",
  "status": 404,
  "error": "Not Found",
  "message": "资源不存在",
  "path": "/api/notifications/999"
}
```

---

## 💡 技术亮点

### 1. 架构设计
- **前后端分离**: JWT无状态认证，支持多端接入
- **微服务友好**: 无状态设计，易于水平扩展
- **事件驱动**: 基于消息的异步通信

### 2. 性能优化
- **长连接**: WebSocket减少连接开销
- **消息代理**: Spring内置消息代理，高效消息分发
- **数据库索引**: 通知表按用户ID和时间索引

### 3. 安全机制
- **JWT认证**: 防止CSRF攻击，支持跨域
- **权限控制**: 基于角色的访问控制
- **输入验证**: 防止XSS和SQL注入

### 4. 可维护性
- **模块化设计**: 功能模块清晰分离
- **配置外部化**: 支持不同环境配置
- **日志记录**: 完整的操作日志

### 5. 用户体验
- **实时反馈**: 即时消息推送
- **离线支持**: 消息持久化存储
- **多端同步**: 支持多设备同时在线

---

## 📈 扩展建议

### 1. 功能扩展
- 添加消息模板系统
- 实现消息推送策略配置
- 添加消息统计和分析
- 支持富文本消息格式

### 2. 性能优化
- 引入Redis作为消息缓存
- 实现消息队列持久化
- 添加消息发送限流机制
- 优化数据库查询性能

### 3. 监控告警
- 添加WebSocket连接监控
- 实现消息发送成功率统计
- 添加系统健康检查接口
- 集成APM监控工具

### 4. 安全增强
- 实现JWT token刷新机制
- 添加API访问频率限制
- 增强WebSocket连接安全
- 实现消息内容加密

---

## 📝 总结

本次实现为图书管理系统成功添加了完整的实时通知和JWT认证功能，主要成果包括：

✅ **完整的JWT认证系统** - 支持用户登录、token验证和权限控制
✅ **实时WebSocket通知** - 支持个人通知和系统广播
✅ **丰富的通知类型** - 新书到达、借阅提醒、系统消息等
✅ **定时任务系统** - 自动检查借阅状态和发送提醒
✅ **完整的测试页面** - 便于功能测试和演示
✅ **RESTful API** - 支持前端集成和第三方调用

该实现采用了现代化的技术栈和最佳实践，具有良好的可扩展性和维护性，能够满足实际生产环境的需求。系统支持高并发访问，提供了完整的安全机制，为用户提供了优秀的实时交互体验。

---

## 📞 技术支持

如需进一步的技术支持或功能扩展，请参考：
- Spring Boot官方文档
- Spring Security参考指南
- Spring WebSocket文档
- JWT官方规范

---

*报告生成时间: 2025年1月*
*项目版本: v1.0.0*
*技术栈: Spring Boot 3.4.5 + JWT + WebSocket*
